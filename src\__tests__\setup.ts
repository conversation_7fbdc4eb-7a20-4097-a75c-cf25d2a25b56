/**
 * Vitest 测试设置文件
 */
import { vi } from 'vitest'

// Mock uni-app API
global.uni = {
  showToast: vi.fn(),
  showLoading: vi.fn(),
  hideLoading: vi.fn(),
  showModal: vi.fn(),
  setNavigationBarTitle: vi.fn(),
  navigateTo: vi.fn(),
  reLaunch: vi.fn(),
  request: vi.fn()
} as any

// Mock console methods for cleaner test output
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
}
