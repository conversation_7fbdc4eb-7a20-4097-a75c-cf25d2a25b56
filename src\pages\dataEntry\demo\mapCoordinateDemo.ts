/**
 * 地图坐标获取功能演示
 * 
 * 此文件演示了如何使用 getValueByKey 接口获取地图初始坐标
 * 并将其应用到地图组件中
 */

import { getValueByKey } from '@/service/userAPI'

/**
 * 演示地图坐标获取功能
 */
export async function demoMapCoordinateFeature() {
  console.log('🚀 开始演示地图坐标获取功能...')
  
  try {
    // 1. 调用 getValueByKey 接口获取地图初始坐标
    console.log('📡 调用 getValueByKey 接口...')
    const response = await getValueByKey('urban.map.init.center')
    
    console.log('✅ 接口响应:', response)
    
    // 2. 解析坐标字符串
    if (response && typeof response === 'string') {
      const coords = response.split(',').map(coord => parseFloat(coord.trim()))
      
      if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
        const [longitude, latitude] = coords
        
        console.log('🗺️ 解析后的坐标:', {
          latitude,
          longitude,
          原始字符串: response
        })
        
        // 3. 模拟地图组件的坐标更新
        const mapConfig = {
          latitude,
          longitude,
          centerMarker: { latitude, longitude },
          scale: 14
        }
        
        console.log('🎯 地图配置已更新:', mapConfig)
        
        return {
          success: true,
          coordinates: { latitude, longitude },
          mapConfig
        }
      } else {
        console.warn('⚠️ 坐标格式解析失败')
        return {
          success: false,
          error: '坐标格式无效',
          rawResponse: response
        }
      }
    } else {
      console.warn('⚠️ 接口返回数据格式不正确')
      return {
        success: false,
        error: '接口返回数据格式不正确',
        rawResponse: response
      }
    }
  } catch (error) {
    console.error('❌ 获取地图坐标失败:', error)
    return {
      success: false,
      error: error.message || '未知错误',
      defaultCoordinates: {
        latitude: 42.881636,
        longitude: 129.382471
      }
    }
  }
}

/**
 * 验证坐标格式的工具函数
 */
export function validateCoordinates(coordString: string): {
  isValid: boolean
  coordinates?: { latitude: number; longitude: number }
  error?: string
} {
  if (!coordString || typeof coordString !== 'string') {
    return {
      isValid: false,
      error: '坐标字符串为空或格式不正确'
    }
  }
  
  const coords = coordString.split(',').map(coord => parseFloat(coord.trim()))
  
  if (coords.length !== 2) {
    return {
      isValid: false,
      error: '坐标应包含两个数值（经度,纬度）'
    }
  }
  
  const [longitude, latitude] = coords
  
  if (isNaN(longitude) || isNaN(latitude)) {
    return {
      isValid: false,
      error: '坐标包含无效的数值'
    }
  }
  
  // 验证坐标范围
  if (latitude < -90 || latitude > 90) {
    return {
      isValid: false,
      error: '纬度应在 -90 到 90 之间'
    }
  }
  
  if (longitude < -180 || longitude > 180) {
    return {
      isValid: false,
      error: '经度应在 -180 到 180 之间'
    }
  }
  
  return {
    isValid: true,
    coordinates: { latitude, longitude }
  }
}

/**
 * 测试不同格式的坐标字符串
 */
export function testCoordinateFormats() {
  const testCases = [
    '129.5040, 42.9156',      // 标准格式
    '129.5040,42.9156',       // 无空格
    '  129.5040  ,  42.9156  ', // 多余空格
    '129.5040',               // 缺少纬度
    'invalid,coordinates',    // 无效数值
    '',                       // 空字符串
    '200, 100'                // 超出范围
  ]
  
  console.log('🧪 测试不同坐标格式:')
  
  testCases.forEach((testCase, index) => {
    const result = validateCoordinates(testCase)
    console.log(`测试 ${index + 1}: "${testCase}"`, result)
  })
}

// 如果在浏览器环境中，可以直接调用演示函数
if (typeof window !== 'undefined') {
  // 延迟执行，确保页面加载完成
  setTimeout(() => {
    console.log('🎬 开始地图坐标功能演示...')
    testCoordinateFormats()
    // demoMapCoordinateFeature() // 需要真实的 API 环境
  }, 1000)
}
