# 地图初始坐标获取功能

## 功能概述

在 `pages/dataEntry/index` 页面中实现了通过接口 `getValueByKey` 获取地图经纬度坐标的功能，并将获取到的坐标设置为地图的初始中心位置。

## 实现详情

### 1. 接口调用

- **接口名称**: `getValueByKey`
- **参数**: `key = 'urban.map.init.center'`
- **返回格式**: 字符串，如 `"129.5040, 42.9156"`
- **坐标格式**: `"经度, 纬度"`

### 2. 代码修改

#### 2.1 useMapData.ts 修改

在 `src/pages/dataEntry/composables/useMapData.ts` 中添加了：

```typescript
import { getValueByKey } from '@/service/userAPI'

/**
 * 获取地图初始坐标
 */
const getInitialMapCenter = async () => {
  try {
    console.log('🗺️ 开始获取地图初始坐标...')
    const response = await getValueByKey('urban.map.init.center')
    
    if (response && typeof response === 'string') {
      // 解析坐标字符串，格式如 "129.5040, 42.9156"
      const coords = response.split(',').map(coord => parseFloat(coord.trim()))
      
      if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
        const [lng, lat] = coords
        
        console.log('✅ 成功获取地图初始坐标:', { latitude: lat, longitude: lng })
        
        // 更新地图中心坐标
        latitude.value = lat
        longitude.value = lng
        centerMarker.value = { latitude: lat, longitude: lng }
        
        return { latitude: lat, longitude: lng }
      } else {
        console.warn('⚠️ 坐标格式解析失败，使用默认坐标')
      }
    } else {
      console.warn('⚠️ 未获取到有效的坐标数据，使用默认坐标')
    }
  } catch (error) {
    console.error('❌ 获取地图初始坐标失败:', error)
  }
  
  // 返回默认坐标（如果获取失败）
  return { latitude: latitude.value, longitude: longitude.value }
}
```

#### 2.2 index.vue 修改

在 `src/pages/dataEntry/index.vue` 的 `initPageData` 函数中添加了坐标获取调用：

```typescript
const initPageData = withErrorHandling(async () => {
  if (!taskId.value) {
    throw new Error('任务ID不存在')
  }

  isLoading.value = true

  try {
    // 首先获取地图初始坐标
    await mapData.getInitialMapCenter()

    // 并行加载数据，传递taskType参数
    const [data] = await Promise.all([taskData.initTaskData(taskId.value, taskType.value)])

    // 处理地图数据
    mapData.clearMapData()
    mapData.processGlobalWktData()
    mapData.processHouseData(data, taskType.value)

    // ... 其他代码
  } catch (error) {
    console.error('❌ 页面数据初始化失败:', error)
    throw error
  } finally {
    isLoading.value = false
  }
}, '页面数据加载失败')
```

### 3. 功能特性

#### 3.1 坐标解析
- 支持标准格式：`"129.5040, 42.9156"`
- 支持无空格格式：`"129.5040,42.9156"`
- 支持多余空格：`"  129.5040  ,  42.9156  "`
- 自动去除前后空格

#### 3.2 错误处理
- API 调用失败时使用默认坐标
- 坐标格式无效时使用默认坐标
- 返回数据为空时使用默认坐标
- 所有错误都有详细的控制台日志

#### 3.3 默认坐标
如果获取失败，将使用以下默认坐标：
- 纬度：42.881636
- 经度：129.382471

### 4. 执行流程

1. 页面加载时调用 `initPageData()`
2. `initPageData()` 首先调用 `mapData.getInitialMapCenter()`
3. `getInitialMapCenter()` 调用 `getValueByKey('urban.map.init.center')`
4. 解析返回的坐标字符串
5. 更新地图的 `latitude`、`longitude` 和 `centerMarker`
6. 继续执行其他数据加载逻辑

### 5. 日志输出

功能执行时会输出以下日志：

```
🗺️ 开始获取地图初始坐标...
✅ 成功获取地图初始坐标: { latitude: 42.9156, longitude: 129.5040 }
```

如果出现错误：
```
⚠️ 坐标格式解析失败，使用默认坐标
❌ 获取地图初始坐标失败: [错误信息]
```

### 6. 测试

项目中包含了测试文件和演示代码：

- **测试文件**: `src/pages/dataEntry/__tests__/useMapData.test.ts`
- **演示代码**: `src/pages/dataEntry/demo/mapCoordinateDemo.ts`

演示代码包含了坐标验证和格式测试功能。

### 7. 使用说明

1. 确保后端接口 `/infra/config/get-value-by-key` 正常工作
2. 确保配置项 `urban.map.init.center` 存在且格式正确
3. 页面加载时会自动获取并应用坐标
4. 可以通过浏览器控制台查看详细的执行日志

### 8. 注意事项

- 坐标格式必须是 `"经度, 纬度"` 的字符串格式
- 经度范围：-180 到 180
- 纬度范围：-90 到 90
- 如果接口返回的坐标无效，系统会自动使用默认坐标
- 功能具有良好的容错性，不会因为坐标获取失败而影响页面正常加载
