/**
 * 测试 useMapData composable 中的地图初始坐标获取功能
 */
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useMapData } from '../composables/useMapData'
import { getValueByKey } from '@/service/userAPI'

// Mock getValueByKey API
vi.mock('@/service/userAPI', () => ({
  getValueByKey: vi.fn()
}))

// Mock useTaskStore
vi.mock('@/store', () => ({
  useTaskStore: () => ({
    getTaskWktGeom: vi.fn(() => ''),
    getTaskStatus: vi.fn(() => 0)
  })
}))

// Mock wktToMapComponents
vi.mock('@/utils/wktConverter', () => ({
  wktToMapComponents: vi.fn(() => ({
    markers: [],
    polygons: [],
    polylines: []
  }))
}))

describe('useMapData - 地图初始坐标获取', () => {
  const mockGetValueByKey = vi.mocked(getValueByKey)
  
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该成功解析坐标字符串并更新地图中心', async () => {
    // 模拟 API 返回坐标字符串
    mockGetValueByKey.mockResolvedValue('129.5040, 42.9156')
    
    const { getInitialMapCenter, latitude, longitude, centerMarker } = useMapData()
    
    const result = await getInitialMapCenter()
    
    // 验证 API 被正确调用
    expect(mockGetValueByKey).toHaveBeenCalledWith('urban.map.init.center')
    
    // 验证返回的坐标
    expect(result).toEqual({
      latitude: 42.9156,
      longitude: 129.5040
    })
    
    // 验证地图状态被正确更新
    expect(latitude.value).toBe(42.9156)
    expect(longitude.value).toBe(129.5040)
    expect(centerMarker.value).toEqual({
      latitude: 42.9156,
      longitude: 129.5040
    })
  })

  it('应该处理不同格式的坐标字符串', async () => {
    // 测试带空格的坐标字符串
    mockGetValueByKey.mockResolvedValue('  129.5040  ,  42.9156  ')
    
    const { getInitialMapCenter } = useMapData()
    
    const result = await getInitialMapCenter()
    
    expect(result).toEqual({
      latitude: 42.9156,
      longitude: 129.5040
    })
  })

  it('应该处理无效的坐标字符串', async () => {
    // 测试无效的坐标字符串
    mockGetValueByKey.mockResolvedValue('invalid,coordinates')
    
    const { getInitialMapCenter, latitude, longitude } = useMapData()
    
    // 保存初始值
    const initialLat = latitude.value
    const initialLng = longitude.value
    
    const result = await getInitialMapCenter()
    
    // 验证返回默认坐标
    expect(result).toEqual({
      latitude: initialLat,
      longitude: initialLng
    })
    
    // 验证地图状态未被更改
    expect(latitude.value).toBe(initialLat)
    expect(longitude.value).toBe(initialLng)
  })

  it('应该处理 API 错误', async () => {
    // 模拟 API 错误
    mockGetValueByKey.mockRejectedValue(new Error('API Error'))
    
    const { getInitialMapCenter, latitude, longitude } = useMapData()
    
    // 保存初始值
    const initialLat = latitude.value
    const initialLng = longitude.value
    
    const result = await getInitialMapCenter()
    
    // 验证返回默认坐标
    expect(result).toEqual({
      latitude: initialLat,
      longitude: initialLng
    })
    
    // 验证地图状态未被更改
    expect(latitude.value).toBe(initialLat)
    expect(longitude.value).toBe(initialLng)
  })

  it('应该处理空的 API 响应', async () => {
    // 测试空响应
    mockGetValueByKey.mockResolvedValue('')
    
    const { getInitialMapCenter, latitude, longitude } = useMapData()
    
    // 保存初始值
    const initialLat = latitude.value
    const initialLng = longitude.value
    
    const result = await getInitialMapCenter()
    
    // 验证返回默认坐标
    expect(result).toEqual({
      latitude: initialLat,
      longitude: initialLng
    })
  })

  it('应该处理非字符串的 API 响应', async () => {
    // 测试非字符串响应
    mockGetValueByKey.mockResolvedValue(null)
    
    const { getInitialMapCenter, latitude, longitude } = useMapData()
    
    // 保存初始值
    const initialLat = latitude.value
    const initialLng = longitude.value
    
    const result = await getInitialMapCenter()
    
    // 验证返回默认坐标
    expect(result).toEqual({
      latitude: initialLat,
      longitude: initialLng
    })
  })
})
